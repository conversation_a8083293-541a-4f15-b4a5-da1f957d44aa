<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyberpunk Reservation Form</title>
    <link rel="stylesheet" href="futuristic-form.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Animated Background -->
    <div class="cyber-background">
        <div class="grid-overlay"></div>
        <div class="floating-particles"></div>
        <div class="neon-lines">
            <div class="line line-1"></div>
            <div class="line line-2"></div>
            <div class="line line-3"></div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <div class="form-wrapper">
            <!-- Header -->
            <div class="form-header">
                <div class="header-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h1 class="form-title">
                    <span class="glitch" data-text="CYBER">CYBER</span>
                    <span class="subtitle">RESERVATION</span>
                </h1>
                <div class="header-line"></div>
            </div>

            <!-- Scrollable Form Container -->
            <div class="form-container">
                <form id="reservationForm" class="cyber-form">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-user-circle"></i>
                            PERSONAL DATA
                        </h3>
                        
                        <div class="input-group">
                            <label for="fullName" class="input-label">
                                <i class="fas fa-user"></i>
                                FULL NAME
                            </label>
                            <input type="text" id="fullName" name="fullName" class="cyber-input" required>
                            <div class="input-border"></div>
                        </div>

                        <div class="input-group">
                            <label for="email" class="input-label">
                                <i class="fas fa-envelope"></i>
                                EMAIL ADDRESS
                            </label>
                            <input type="email" id="email" name="email" class="cyber-input" required>
                            <div class="input-border"></div>
                        </div>

                        <div class="input-group">
                            <label for="phone" class="input-label">
                                <i class="fas fa-phone"></i>
                                PHONE NUMBER
                            </label>
                            <input type="tel" id="phone" name="phone" class="cyber-input" required>
                            <div class="input-border"></div>
                        </div>
                    </div>

                    <!-- Reservation Details Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-calendar-alt"></i>
                            RESERVATION DETAILS
                        </h3>

                        <div class="input-group">
                            <label for="date" class="input-label">
                                <i class="fas fa-calendar"></i>
                                RESERVATION DATE
                            </label>
                            <input type="date" id="date" name="date" class="cyber-input" required>
                            <div class="input-border"></div>
                        </div>

                        <div class="time-row">
                            <div class="input-group half-width">
                                <label for="startTime" class="input-label">
                                    <i class="fas fa-clock"></i>
                                    START TIME
                                </label>
                                <input type="time" id="startTime" name="startTime" class="cyber-input" required>
                                <div class="input-border"></div>
                            </div>

                            <div class="input-group half-width">
                                <label for="endTime" class="input-label">
                                    <i class="fas fa-clock"></i>
                                    END TIME
                                </label>
                                <input type="time" id="endTime" name="endTime" class="cyber-input" required>
                                <div class="input-border"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Options Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-cogs"></i>
                            ADDITIONAL OPTIONS
                        </h3>

                        <div class="checkbox-group">
                            <label class="cyber-checkbox">
                                <input type="checkbox" id="vipAccess" name="vipAccess">
                                <span class="checkmark"></span>
                                <span class="checkbox-label">VIP ACCESS UPGRADE</span>
                            </label>
                        </div>

                        <div class="checkbox-group">
                            <label class="cyber-checkbox">
                                <input type="checkbox" id="notifications" name="notifications">
                                <span class="checkmark"></span>
                                <span class="checkbox-label">RECEIVE NOTIFICATIONS</span>
                            </label>
                        </div>

                        <div class="input-group">
                            <label for="specialRequests" class="input-label">
                                <i class="fas fa-comment"></i>
                                SPECIAL REQUESTS
                            </label>
                            <textarea id="specialRequests" name="specialRequests" class="cyber-textarea" rows="4" placeholder="Enter any special requirements..."></textarea>
                            <div class="input-border"></div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="submit-section">
                        <button type="submit" class="cyber-submit-btn">
                            <span class="btn-text">INITIATE RESERVATION</span>
                            <span class="btn-icon"><i class="fas fa-rocket"></i></span>
                            <div class="btn-glow"></div>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="form-footer">
                <div class="footer-text">SECURE CONNECTION ESTABLISHED</div>
                <div class="status-indicators">
                    <span class="status-dot active"></span>
                    <span class="status-dot active"></span>
                    <span class="status-dot active"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <i class="fas fa-check-circle"></i>
                <h2>RESERVATION CONFIRMED</h2>
            </div>
            <div class="modal-body" id="confirmationDetails">
                <!-- Confirmation details will be inserted here -->
            </div>
            <button class="modal-close-btn" onclick="closeModal()">
                <i class="fas fa-times"></i>
                CLOSE
            </button>
        </div>
    </div>

    <script src="futuristic-form.js"></script>
</body>
</html>
