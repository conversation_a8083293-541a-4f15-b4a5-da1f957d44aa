<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GameHub Arena - Ultimate Gaming Experience with PC and PS5 stations. Reserve your gaming station now!">
    <meta name="keywords" content="gaming, PC gaming, PS5, gaming lounge, reservations">
    <title>GameHub Arena - Ultimate Gaming Experience</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="script.js" as="script">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

    <!-- Critical CSS (inline small critical styles) -->
    <style>
        /* Critical above-the-fold styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Rajdhani', sans-serif; background: #0a0a0a; color: #ffffff; line-height: 1.6; overflow-x: hidden; }
        .hero { min-height: 100vh; display: flex; align-items: center; justify-content: center; position: relative; }
        .navbar { position: fixed; top: 0; width: 100%; z-index: 2000; background: rgba(26, 26, 46, 0.95); backdrop-filter: blur(20px); }
    </style>

    <!-- Load main CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Load fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet"></noscript>

    <!-- Load Font Awesome asynchronously -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>
</head>
<body>
    <!-- Enhanced Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-gamepad logo-icon"></i>
                <span class="logo-text">GameHub Arena</span>
                <div class="logo-glow"></div>
            </div>

            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link" data-section="home">
                        <i class="fas fa-home nav-icon"></i>
                        <span>Home</span>
                        <div class="nav-link-bg"></div>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#hall" class="nav-link" data-section="hall">
                        <i class="fas fa-map nav-icon"></i>
                        <span>Gaming Hall</span>
                        <div class="nav-link-bg"></div>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link" data-section="about">
                        <i class="fas fa-info-circle nav-icon"></i>
                        <span>About</span>
                        <div class="nav-link-bg"></div>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link" data-section="contact">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span>Contact</span>
                        <div class="nav-link-bg"></div>
                    </a>
                </li>
            </ul>

            <div class="nav-actions">
                <button class="nav-btn reservation-btn" onclick="scrollToHall()">
                    <i class="fas fa-calendar-plus"></i>
                    <span>Reserve Now</span>
                </button>
                <div class="theme-toggle" id="theme-toggle">
                    <i class="fas fa-moon"></i>
                </div>
            </div>

            <div class="hamburger" id="hamburger">
                <span class="bar bar-1"></span>
                <span class="bar bar-2"></span>
                <span class="bar bar-3"></span>
            </div>
        </div>

        <!-- Navigation Progress Indicator -->
        <div class="nav-progress" id="nav-progress"></div>
    </nav>

    <!-- Optimized Animated Background System -->
    <div class="animated-background" id="animated-background">
        <!-- Simplified Neon Wave System -->
        <div class="neon-waves-container">
            <div class="neon-wave wave-1"></div>
            <div class="neon-wave wave-2"></div>
        </div>

        <!-- Reduced Geometric Shapes -->
        <div class="geometric-shapes-container" id="geometric-shapes">
            <div class="geometric-shape triangle-1"></div>
            <div class="geometric-shape hexagon-1"></div>
        </div>

        <!-- Simplified Particle System -->
        <div class="particles-system">
            <div class="floating-particles-container" id="floating-particles"></div>
        </div>

        <!-- Reduced Glowing Orbs -->
        <div class="glowing-orbs-container">
            <div class="glowing-orb orb-1">
                <div class="orb-core"></div>
            </div>
            <div class="glowing-orb orb-2">
                <div class="orb-core"></div>
            </div>
        </div>

        <!-- Simplified Circuit Pattern -->
        <div class="circuit-pattern">
            <div class="circuit-grid"></div>
        </div>
    </div>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-particles"></div>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">
                <span class="glitch" data-text="GAMEHUB">GAMEHUB</span>
                <span class="arena-text">ARENA</span>
            </h1>
            <p class="hero-subtitle">Experience the Future of Gaming</p>
            <div class="hero-stats">
                <div class="stat">
                    <span class="stat-number">20</span>
                    <span class="stat-label">PC Stations</span>
                </div>
                <div class="stat">
                    <span class="stat-number">5</span>
                    <span class="stat-label">PS5 Consoles</span>
                </div>
                <div class="stat">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">Open</span>
                </div>
            </div>
            <button class="cta-button" onclick="scrollToHall()">
                <span>Reserve Your Station</span>
                <i class="fas fa-arrow-down"></i>
            </button>
        </div>
    </section>

    <!-- Gaming Hall Section -->
    <section id="hall" class="gaming-hall">
        <div class="container">
            <h2 class="section-title">Interactive Gaming Hall</h2>
            <p class="section-subtitle">Click on any station to make a reservation</p>
            
            <!-- Status Legend -->
            <div class="status-legend">
                <div class="legend-item">
                    <div class="status-indicator available"></div>
                    <span>Available</span>
                </div>
                <div class="legend-item">
                    <div class="status-indicator reserved"></div>
                    <span>Reserved</span>
                </div>
                <div class="legend-item">
                    <div class="status-indicator in-use-soon"></div>
                    <span>In Use Soon</span>
                </div>
            </div>

            <!-- Enhanced 3D-Style Hall Layout -->
            <div class="hall-layout">
                <div class="hall-container">
                    <!-- Hall Environment Elements -->
                    <div class="hall-environment">
                        <div class="hall-walls">
                            <div class="wall wall-top"></div>
                            <div class="wall wall-right"></div>
                            <div class="wall wall-bottom"></div>
                            <div class="wall wall-left"></div>
                        </div>
                        <div class="hall-floor">
                            <div class="floor-grid"></div>
                            <div class="floor-lighting"></div>
                        </div>
                        <div class="hall-ceiling">
                            <div class="ceiling-lights" id="ceiling-lights"></div>
                        </div>
                    </div>

                    <!-- Gaming Areas -->
                    <div class="gaming-areas">
                        <!-- PC Gaming Zone -->
                        <div class="gaming-zone pc-zone">
                            <div class="zone-header">
                                <h3 class="zone-title">
                                    <i class="fas fa-desktop zone-icon"></i>
                                    PC Gaming Stations
                                </h3>
                                <div class="zone-capacity">
                                    <span id="pc-available">20</span>/<span>20</span> Available
                                </div>
                            </div>
                            <div class="zone-layout">
                                <div id="pc-stations" class="stations-grid pc-grid"></div>
                                <div class="zone-features">
                                    <div class="feature-item">
                                        <i class="fas fa-wifi"></i>
                                        <span>High-Speed Internet</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-headphones"></i>
                                        <span>Premium Audio</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- PS5 Gaming Zone -->
                        <div class="gaming-zone ps5-zone">
                            <div class="zone-header">
                                <h3 class="zone-title">
                                    <i class="fab fa-playstation zone-icon"></i>
                                    PlayStation 5 Consoles
                                </h3>
                                <div class="zone-capacity">
                                    <span id="ps5-available">5</span>/<span>5</span> Available
                                </div>
                            </div>
                            <div class="zone-layout">
                                <div id="ps5-stations" class="stations-grid ps5-grid"></div>
                                <div class="zone-features">
                                    <div class="feature-item">
                                        <i class="fas fa-tv"></i>
                                        <span>4K Gaming</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-gamepad"></i>
                                        <span>DualSense Controllers</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Common Areas -->
                        <div class="common-areas">
                            <div class="lounge-area">
                                <i class="fas fa-couch"></i>
                                <span>Lounge</span>
                            </div>
                            <div class="snack-bar">
                                <i class="fas fa-coffee"></i>
                                <span>Snack Bar</span>
                            </div>
                            <div class="restroom">
                                <i class="fas fa-restroom"></i>
                                <span>Restroom</span>
                            </div>
                        </div>
                    </div>

                    <!-- Multi-Selection Panel -->
                    <div class="multi-selection-panel" id="multi-selection-panel">
                        <div class="panel-header">
                            <h4>Selected Stations</h4>
                            <button class="clear-selection" id="clear-selection">
                                <i class="fas fa-times"></i>
                                Clear All
                            </button>
                        </div>
                        <div class="selected-stations" id="selected-stations"></div>
                        <div class="panel-actions">
                            <button class="reserve-selected" id="reserve-selected">
                                <i class="fas fa-calendar-check"></i>
                                Reserve Selected (<span id="selection-count">0</span>)
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Status Bar -->
            <div class="status-bar">
                <div class="status-info">
                    <span id="available-count">25</span> Available | 
                    <span id="reserved-count">0</span> Reserved | 
                    <span id="in-use-count">0</span> In Use Soon
                </div>
                <div class="last-updated">
                    Last updated: <span id="last-updated-time">Just now</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Reservation Modal -->
    <div id="reservation-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Reserve Gaming Station</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="selected-station-info">
                    <div class="station-preview">
                        <div id="selected-station-icon"></div>
                        <div class="station-details">
                            <h4 id="selected-station-name">PC Station 1</h4>
                            <p id="selected-station-specs">High-end gaming PC</p>
                        </div>
                    </div>
                </div>
                
                <form id="reservation-form" class="reservation-form">
                    <div class="form-group">
                        <label for="customer-name">Full Name *</label>
                        <input type="text" id="customer-name" name="name" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="customer-email">Email Address *</label>
                        <input type="email" id="customer-email" name="email" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="customer-phone">Phone Number *</label>
                        <input type="tel" id="customer-phone" name="phone" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="reservation-date">Date *</label>
                            <input type="date" id="reservation-date" name="date" required>
                            <span class="error-message"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="start-time">Start Time *</label>
                            <input type="time" id="start-time" name="startTime" required>
                            <span class="error-message"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="end-time">End Time *</label>
                            <input type="time" id="end-time" name="endTime" required>
                            <span class="error-message"></span>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeModal()">Cancel</button>
                        <button type="submit" class="btn-reserve">
                            <i class="fas fa-calendar-check"></i>
                            Confirm Reservation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Multi-Reservation Modal -->
    <div id="multi-reservation-modal" class="modal">
        <div class="modal-content multi-reservation-content">
            <div class="modal-header">
                <h3 id="multi-modal-title">Reserve Multiple Stations</h3>
                <span class="close-modal" onclick="closeMultiReservationModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="selected-stations-summary">
                    <h4>Selected Stations (<span id="multi-selection-count">0</span>)</h4>
                    <div class="stations-list" id="multi-stations-list"></div>
                </div>

                <form id="multi-reservation-form" class="reservation-form">
                    <div class="form-group">
                        <label for="multi-customer-name">Group Leader Name *</label>
                        <input type="text" id="multi-customer-name" name="name" required>
                        <span class="error-message"></span>
                    </div>

                    <div class="form-group">
                        <label for="multi-customer-email">Email Address *</label>
                        <input type="email" id="multi-customer-email" name="email" required>
                        <span class="error-message"></span>
                    </div>

                    <div class="form-group">
                        <label for="multi-customer-phone">Phone Number *</label>
                        <input type="tel" id="multi-customer-phone" name="phone" required>
                        <span class="error-message"></span>
                    </div>

                    <div class="form-group">
                        <label for="total-guests">Total Number of Guests *</label>
                        <input type="number" id="total-guests" name="guests" min="1" required>
                        <span class="error-message"></span>
                        <div class="field-hint">Including yourself</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="multi-reservation-date">Date *</label>
                            <input type="date" id="multi-reservation-date" name="date" required>
                            <span class="error-message"></span>
                        </div>

                        <div class="form-group">
                            <label for="multi-start-time">Start Time *</label>
                            <input type="time" id="multi-start-time" name="startTime" required>
                            <span class="error-message"></span>
                        </div>

                        <div class="form-group">
                            <label for="multi-end-time">End Time *</label>
                            <input type="time" id="multi-end-time" name="endTime" required>
                            <span class="error-message"></span>
                        </div>
                    </div>

                    <div class="pricing-summary" id="pricing-summary">
                        <div class="pricing-item">
                            <span>PC Stations (<span id="pc-count">0</span>):</span>
                            <span id="pc-total">$0.00</span>
                        </div>
                        <div class="pricing-item">
                            <span>PS5 Stations (<span id="ps5-count">0</span>):</span>
                            <span id="ps5-total">$0.00</span>
                        </div>
                        <div class="pricing-total">
                            <span>Total:</span>
                            <span id="grand-total">$0.00</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeMultiReservationModal()">Cancel</button>
                        <button type="submit" class="btn-reserve">
                            <i class="fas fa-calendar-check"></i>
                            Confirm Group Reservation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="modal">
        <div class="modal-content confirmation-content">
            <div class="confirmation-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>Reservation Confirmed!</h3>
            </div>
            <div class="confirmation-body">
                <div id="confirmation-details"></div>
                <button class="btn-close-confirmation" onclick="closeConfirmationModal()">
                    <i class="fas fa-times"></i>
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts - Load asynchronously for better performance -->
    <script src="script.js" defer></script>

    <!-- Performance optimization: Preload next page resources -->
    <link rel="prefetch" href="styles.css">
</body>
</html>
