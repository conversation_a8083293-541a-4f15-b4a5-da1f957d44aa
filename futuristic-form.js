// Cyberpunk Futuristic Form JavaScript

// Initialize the form when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    createFloatingParticles();
    setupFormValidation();
    setupDateRestrictions();
});

/**
 * Initialize form functionality
 */
function initializeForm() {
    const form = document.getElementById('reservationForm');
    
    // Handle form submission
    form.addEventListener('submit', handleFormSubmission);
    
    // Add input focus effects
    setupInputEffects();
    
    // Add typing sound effects (visual feedback)
    setupTypingEffects();
    
    console.log('🚀 Cyberpunk form initialized successfully!');
}

/**
 * Handle form submission
 */
function handleFormSubmission(event) {
    event.preventDefault();
    
    // Add submission animation
    const submitBtn = document.querySelector('.cyber-submit-btn');
    submitBtn.style.transform = 'scale(0.95)';
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> PROCESSING...';
    
    // Collect form data
    const formData = collectFormData();
    
    // Validate form data
    if (validateFormData(formData)) {
        // Simulate processing delay
        setTimeout(() => {
            showConfirmationModal(formData);
            resetSubmitButton();
        }, 2000);
    } else {
        resetSubmitButton();
        showError('Please fill in all required fields correctly.');
    }
}

/**
 * Collect all form data
 */
function collectFormData() {
    const form = document.getElementById('reservationForm');
    const formData = new FormData(form);
    
    return {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        date: formData.get('date'),
        startTime: formData.get('startTime'),
        endTime: formData.get('endTime'),
        vipAccess: formData.get('vipAccess') === 'on',
        notifications: formData.get('notifications') === 'on',
        specialRequests: formData.get('specialRequests') || 'None'
    };
}

/**
 * Validate form data
 */
function validateFormData(data) {
    // Check required fields
    if (!data.fullName || !data.email || !data.phone || !data.date || !data.startTime || !data.endTime) {
        return false;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
        showError('Please enter a valid email address.');
        return false;
    }
    
    // Validate phone format (basic)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
        showError('Please enter a valid phone number.');
        return false;
    }
    
    // Validate time range
    if (data.startTime >= data.endTime) {
        showError('End time must be after start time.');
        return false;
    }
    
    // Validate date (not in the past)
    const selectedDate = new Date(data.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
        showError('Please select a future date.');
        return false;
    }
    
    return true;
}

/**
 * Show confirmation modal with form data
 */
function showConfirmationModal(data) {
    const modal = document.getElementById('confirmationModal');
    const detailsContainer = document.getElementById('confirmationDetails');
    
    // Format the confirmation details
    const confirmationHTML = `
        <div class="confirmation-item">
            <strong>👤 Name:</strong> ${data.fullName}
        </div>
        <div class="confirmation-item">
            <strong>📧 Email:</strong> ${data.email}
        </div>
        <div class="confirmation-item">
            <strong>📞 Phone:</strong> ${data.phone}
        </div>
        <div class="confirmation-item">
            <strong>📅 Date:</strong> ${formatDate(data.date)}
        </div>
        <div class="confirmation-item">
            <strong>⏰ Time:</strong> ${formatTime(data.startTime)} - ${formatTime(data.endTime)}
        </div>
        <div class="confirmation-item">
            <strong>⭐ VIP Access:</strong> ${data.vipAccess ? 'Yes' : 'No'}
        </div>
        <div class="confirmation-item">
            <strong>🔔 Notifications:</strong> ${data.notifications ? 'Enabled' : 'Disabled'}
        </div>
        <div class="confirmation-item">
            <strong>💬 Special Requests:</strong> ${data.specialRequests}
        </div>
    `;
    
    detailsContainer.innerHTML = confirmationHTML;
    modal.style.display = 'block';
    
    // Add success sound effect (visual feedback)
    createSuccessParticles();
    
    // Auto-close after 10 seconds
    setTimeout(() => {
        closeModal();
    }, 10000);
}

/**
 * Close the confirmation modal
 */
function closeModal() {
    const modal = document.getElementById('confirmationModal');
    modal.style.display = 'none';
    
    // Reset form
    document.getElementById('reservationForm').reset();
    
    // Show success message
    showSuccess('Form reset successfully. Ready for new reservation!');
}

/**
 * Reset submit button to original state
 */
function resetSubmitButton() {
    const submitBtn = document.querySelector('.cyber-submit-btn');
    submitBtn.style.transform = '';
    submitBtn.innerHTML = `
        <span class="btn-text">INITIATE RESERVATION</span>
        <span class="btn-icon"><i class="fas fa-rocket"></i></span>
        <div class="btn-glow"></div>
    `;
}

/**
 * Setup input focus effects
 */
function setupInputEffects() {
    const inputs = document.querySelectorAll('.cyber-input, .cyber-textarea');
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            createInputGlow(this);
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
        
        input.addEventListener('input', function() {
            if (this.value) {
                this.parentElement.classList.add('has-value');
            } else {
                this.parentElement.classList.remove('has-value');
            }
        });
    });
}

/**
 * Setup typing effects for inputs
 */
function setupTypingEffects() {
    const inputs = document.querySelectorAll('.cyber-input, .cyber-textarea');
    
    inputs.forEach(input => {
        input.addEventListener('keydown', function(e) {
            // Create typing particle effect
            if (e.key.length === 1) { // Only for character keys
                createTypingParticle(this);
            }
        });
    });
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    const inputs = document.querySelectorAll('.cyber-input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateInput(this);
        });
    });
}

/**
 * Setup date restrictions
 */
function setupDateRestrictions() {
    const dateInput = document.getElementById('date');
    const today = new Date().toISOString().split('T')[0];
    dateInput.setAttribute('min', today);
}

/**
 * Validate individual input
 */
function validateInput(input) {
    const value = input.value.trim();
    const inputGroup = input.parentElement;
    
    // Remove existing error states
    inputGroup.classList.remove('error');
    
    if (input.hasAttribute('required') && !value) {
        inputGroup.classList.add('error');
        return false;
    }
    
    // Email validation
    if (input.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            inputGroup.classList.add('error');
            return false;
        }
    }
    
    return true;
}

/**
 * Create floating particles in background
 */
function createFloatingParticles() {
    const particlesContainer = document.querySelector('.floating-particles');
    const particleCount = 20;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: ${Math.random() > 0.5 ? 'var(--cyber-primary)' : 'var(--cyber-secondary)'};
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: floatParticle ${Math.random() * 10 + 10}s linear infinite;
            animation-delay: ${Math.random() * 5}s;
            opacity: 0.6;
        `;
        
        particlesContainer.appendChild(particle);
    }
}

/**
 * Create input glow effect
 */
function createInputGlow(input) {
    const rect = input.getBoundingClientRect();
    const glow = document.createElement('div');
    glow.style.cssText = `
        position: fixed;
        left: ${rect.left}px;
        top: ${rect.top}px;
        width: ${rect.width}px;
        height: ${rect.height}px;
        border: 2px solid var(--cyber-primary);
        border-radius: 8px;
        pointer-events: none;
        z-index: 1000;
        animation: inputGlowEffect 0.5s ease-out forwards;
    `;
    
    document.body.appendChild(glow);
    
    setTimeout(() => {
        document.body.removeChild(glow);
    }, 500);
}

/**
 * Create typing particle effect
 */
function createTypingParticle(input) {
    const rect = input.getBoundingClientRect();
    const particle = document.createElement('div');
    
    particle.style.cssText = `
        position: fixed;
        left: ${rect.left + Math.random() * rect.width}px;
        top: ${rect.top + rect.height / 2}px;
        width: 3px;
        height: 3px;
        background: var(--cyber-primary);
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        animation: typingParticle 0.8s ease-out forwards;
    `;
    
    document.body.appendChild(particle);
    
    setTimeout(() => {
        document.body.removeChild(particle);
    }, 800);
}

/**
 * Create success particles
 */
function createSuccessParticles() {
    for (let i = 0; i < 15; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: fixed;
            left: 50%;
            top: 50%;
            width: 6px;
            height: 6px;
            background: var(--cyber-success);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1001;
            animation: successParticle 2s ease-out forwards;
            animation-delay: ${i * 0.1}s;
        `;
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            if (document.body.contains(particle)) {
                document.body.removeChild(particle);
            }
        }, 2000);
    }
}

/**
 * Utility functions
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function showError(message) {
    // Create error notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--cyber-danger);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1002;
        animation: slideInRight 0.3s ease;
        box-shadow: 0 5px 15px rgba(255, 0, 64, 0.3);
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function showSuccess(message) {
    // Create success notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--cyber-success);
        color: var(--cyber-dark);
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1002;
        animation: slideInRight 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 255, 0, 0.3);
        font-weight: 600;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes floatParticle {
        0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
        10% { opacity: 0.6; }
        90% { opacity: 0.6; }
        100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
    }
    
    @keyframes inputGlowEffect {
        0% { opacity: 0; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.05); }
        100% { opacity: 0; transform: scale(1.1); }
    }
    
    @keyframes typingParticle {
        0% { opacity: 1; transform: translateY(0) scale(1); }
        100% { opacity: 0; transform: translateY(-20px) scale(0); }
    }
    
    @keyframes successParticle {
        0% { opacity: 1; transform: translate(-50%, -50%) scale(0); }
        50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        100% { 
            opacity: 0; 
            transform: translate(
                calc(-50% + ${Math.random() * 200 - 100}px), 
                calc(-50% + ${Math.random() * 200 - 100}px)
            ) scale(0); 
        }
    }
    
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .input-group.focused .input-label {
        color: var(--cyber-primary);
        text-shadow: 0 0 10px var(--cyber-primary);
    }
    
    .input-group.error .cyber-input,
    .input-group.error .cyber-textarea {
        border-color: var(--cyber-danger);
        box-shadow: 0 0 15px rgba(255, 0, 64, 0.3);
    }
    
    .confirmation-item {
        margin-bottom: 15px;
        padding: 10px;
        background: rgba(0, 255, 255, 0.05);
        border-left: 3px solid var(--cyber-primary);
        border-radius: 4px;
        font-family: 'Rajdhani', sans-serif;
        font-size: 1.1rem;
    }
    
    .confirmation-item strong {
        color: var(--cyber-primary);
        margin-right: 10px;
    }
`;

document.head.appendChild(style);

console.log('🎮 Cyberpunk form fully loaded and ready for action!');
