/* Cyberpunk Futuristic Form Styles */

:root {
    --cyber-primary: #00ffff;
    --cyber-secondary: #ff00ff;
    --cyber-accent: #ffff00;
    --cyber-success: #00ff00;
    --cyber-danger: #ff0040;
    --cyber-dark: #0a0a0a;
    --cyber-darker: #050505;
    --cyber-card: #1a1a2e;
    --cyber-text: #ffffff;
    --cyber-text-dim: #b0b0b0;
    --cyber-border: rgba(0, 255, 255, 0.3);
    --cyber-glow: 0 0 20px rgba(0, 255, 255, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Rajdhani', sans-serif;
    background: var(--cyber-dark);
    color: var(--cyber-text);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
.cyber-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03) 0%, transparent 50%);
}

.neon-lines {
    position: absolute;
    width: 100%;
    height: 100%;
}

.line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-primary), transparent);
    animation: lineMove 8s linear infinite;
}

.line-1 {
    top: 20%;
    width: 100%;
    animation-delay: 0s;
}

.line-2 {
    top: 50%;
    width: 100%;
    animation-delay: 2s;
}

.line-3 {
    top: 80%;
    width: 100%;
    animation-delay: 4s;
}

@keyframes lineMove {
    0% { transform: translateX(-100%); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}

/* Main Container */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.form-wrapper {
    background: rgba(26, 26, 46, 0.9);
    border: 2px solid var(--cyber-border);
    border-radius: 15px;
    box-shadow: var(--cyber-glow), inset 0 0 20px rgba(0, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    width: 100%;
    max-width: 600px;
    position: relative;
    overflow: hidden;
}

/* Header */
.form-header {
    text-align: center;
    padding: 30px 20px 20px;
    border-bottom: 1px solid var(--cyber-border);
    position: relative;
}

.header-icon {
    font-size: 3rem;
    color: var(--cyber-primary);
    margin-bottom: 15px;
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.form-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 10px;
}

.glitch {
    position: relative;
    color: var(--cyber-primary);
    text-shadow: 0 0 20px var(--cyber-primary);
    animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch::before {
    animation: glitch-1 0.5s infinite;
    color: var(--cyber-secondary);
    z-index: -1;
}

.glitch::after {
    animation: glitch-2 0.5s infinite;
    color: var(--cyber-accent);
    z-index: -2;
}

@keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(-2px, -2px); }
    20% { transform: translate(2px, 2px); }
}

@keyframes glitch-2 {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(2px, 2px); }
    20% { transform: translate(-2px, -2px); }
}

.subtitle {
    display: block;
    color: var(--cyber-secondary);
    font-size: 1.2rem;
    margin-top: 5px;
    letter-spacing: 3px;
}

.header-line {
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-primary), transparent);
    margin: 20px auto 0;
    animation: linePulse 2s ease-in-out infinite;
}

@keyframes linePulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.2); }
}

/* Scrollable Form Container */
.form-container {
    height: 500px; /* Fixed height for scrolling */
    overflow-y: auto;
    padding: 0;
    position: relative;
}

/* Custom Scrollbar */
.form-container::-webkit-scrollbar {
    width: 8px;
}

.form-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

.form-container::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--cyber-primary), var(--cyber-secondary));
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.form-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--cyber-secondary), var(--cyber-primary));
}

/* Form Styles */
.cyber-form {
    padding: 30px;
}

.form-section {
    margin-bottom: 40px;
    position: relative;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    color: var(--cyber-primary);
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--cyber-border);
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    font-size: 1.4rem;
    animation: iconGlow 2s ease-in-out infinite alternate;
}

@keyframes iconGlow {
    0% { text-shadow: 0 0 5px var(--cyber-primary); }
    100% { text-shadow: 0 0 20px var(--cyber-primary), 0 0 30px var(--cyber-primary); }
}

/* Input Groups */
.input-group {
    margin-bottom: 25px;
    position: relative;
}

.input-group.half-width {
    width: 48%;
    display: inline-block;
}

.time-row {
    display: flex;
    justify-content: space-between;
    gap: 4%;
}

.input-label {
    display: block;
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    color: var(--cyber-text-dim);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-label i {
    color: var(--cyber-primary);
    font-size: 1rem;
}

/* Cyber Inputs */
.cyber-input,
.cyber-textarea {
    width: 100%;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid var(--cyber-border);
    border-radius: 8px;
    color: var(--cyber-text);
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.cyber-input:focus,
.cyber-textarea:focus {
    outline: none;
    border-color: var(--cyber-primary);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    background: rgba(0, 255, 255, 0.05);
}

.cyber-input::placeholder,
.cyber-textarea::placeholder {
    color: var(--cyber-text-dim);
    opacity: 0.7;
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--cyber-primary), var(--cyber-secondary));
    transition: width 0.3s ease;
    z-index: 2;
}

.cyber-input:focus + .input-border,
.cyber-textarea:focus + .input-border {
    width: 100%;
}

/* Checkboxes */
.checkbox-group {
    margin-bottom: 20px;
}

.cyber-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 1rem;
    color: var(--cyber-text-dim);
    transition: color 0.3s ease;
}

.cyber-checkbox:hover {
    color: var(--cyber-text);
}

.cyber-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--cyber-border);
    border-radius: 4px;
    margin-right: 15px;
    position: relative;
    transition: all 0.3s ease;
}

.cyber-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--cyber-primary);
    border-color: var(--cyber-primary);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.cyber-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--cyber-dark);
    font-weight: bold;
    font-size: 14px;
}

.checkbox-label {
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* Submit Button */
.submit-section {
    text-align: center;
    margin-top: 40px;
}

.cyber-submit-btn {
    background: linear-gradient(45deg, var(--cyber-primary), var(--cyber-secondary));
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    color: var(--cyber-dark);
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 0 auto;
    box-shadow: 0 5px 20px rgba(0, 255, 255, 0.3);
}

.cyber-submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.5);
}

.cyber-submit-btn:active {
    transform: translateY(-1px);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.cyber-submit-btn:hover .btn-glow {
    left: 100%;
}

.btn-icon {
    font-size: 1.2rem;
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(5px); }
}

/* Footer */
.form-footer {
    padding: 20px;
    border-top: 1px solid var(--cyber-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
}

.footer-text {
    font-family: 'Orbitron', monospace;
    font-size: 0.8rem;
    color: var(--cyber-success);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-indicators {
    display: flex;
    gap: 8px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--cyber-text-dim);
    animation: statusPulse 2s ease-in-out infinite;
}

.status-dot.active {
    background: var(--cyber-success);
    box-shadow: 0 0 10px var(--cyber-success);
}

.status-dot:nth-child(2) {
    animation-delay: 0.3s;
}

.status-dot:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes statusPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--cyber-card);
    margin: 10% auto;
    padding: 30px;
    border: 2px solid var(--cyber-primary);
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--cyber-glow);
    animation: modalSlideIn 0.3s ease;
    text-align: center;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    margin-bottom: 25px;
}

.modal-header i {
    font-size: 3rem;
    color: var(--cyber-success);
    margin-bottom: 15px;
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.modal-header h2 {
    font-family: 'Orbitron', monospace;
    color: var(--cyber-primary);
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.modal-body {
    text-align: left;
    margin-bottom: 25px;
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--cyber-border);
}

.modal-close-btn {
    background: linear-gradient(45deg, var(--cyber-danger), #ff6b6b);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.modal-close-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 0, 64, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .form-wrapper {
        margin: 10px;
    }
    
    .form-container {
        height: 400px;
    }
    
    .time-row {
        flex-direction: column;
        gap: 0;
    }
    
    .input-group.half-width {
        width: 100%;
        display: block;
    }
    
    .form-title {
        font-size: 2rem;
    }
    
    .cyber-form {
        padding: 20px;
    }
}
