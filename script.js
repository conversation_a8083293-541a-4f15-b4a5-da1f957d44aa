// GameHub Arena - Interactive Gaming Hall Reservation System
// Optimized for Performance

// Performance Optimization Utilities
const PerformanceUtils = {
    // Lazy loading for images
    lazyLoadImages: function() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    },

    // Throttle function for performance
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    // Debounce function for performance
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Global variables
let stations = [];
let selectedStation = null;
let selectedStations = new Set(); // For multi-selection
let reservations = JSON.parse(localStorage.getItem('reservations')) || [];
let multiSelectionMode = false;

// Station configurations
const stationConfigs = {
    pc: {
        count: 20,
        icon: 'fas fa-desktop',
        name: 'PC Station',
        specs: 'High-end gaming PC with RTX 4080'
    },
    ps5: {
        count: 5,
        icon: 'fab fa-playstation',
        name: 'PlayStation 5',
        specs: 'Sony PlayStation 5 Console'
    }
};

// Initialize the application with performance optimizations
document.addEventListener('DOMContentLoaded', function() {
    // Initialize core functionality first
    initializeStations();
    setupEventListeners();
    setupMultiSelection();
    updateStatusBar();
    setMinDate();

    // Initialize enhanced features with delay for better performance
    requestAnimationFrame(() => {
        initializeEnhancedNavbar();
        initializeEnhancedHall();

        // Initialize background animations after a short delay
        setTimeout(() => {
            initializeEnhancedBackground();
        }, 100);

        // Start status updater after everything is loaded
        setTimeout(() => {
            startStatusUpdater();
        }, 500);
    });
});

// Enhanced Hall Initialization
function initializeEnhancedHall() {
    createCeilingLights();
    setupHallInteractions();
}

/**
 * Creates ceiling lights for the hall
 */
function createCeilingLights() {
    const ceilingLights = document.getElementById('ceiling-lights');
    const lightCount = 8;

    for (let i = 0; i < lightCount; i++) {
        const light = document.createElement('div');
        light.className = 'ceiling-light';
        light.style.animationDelay = `${i * 0.3}s`;
        ceilingLights.appendChild(light);
    }
}

/**
 * Sets up hall interaction effects
 */
function setupHallInteractions() {
    const hallContainer = document.querySelector('.hall-container');

    // Add hover effects to gaming zones
    const gamingZones = document.querySelectorAll('.gaming-zone');
    gamingZones.forEach(zone => {
        zone.addEventListener('mouseenter', () => {
            zone.style.transform = 'scale(1.02)';
        });

        zone.addEventListener('mouseleave', () => {
            zone.style.transform = 'scale(1)';
        });
    });
}

// Enhanced Background Animation System
function initializeEnhancedBackground() {
    createEnhancedParticles();
    createEnergyParticles();
    createSparkParticles();
    createCircuitNodes();
    createDataStreams();
    createLightRays();
    setupInteractiveBackground();
}

/**
 * Creates enhanced floating particles with varied effects
 */
function createEnhancedParticles() {
    const particlesContainer = document.getElementById('floating-particles');
    const particleCount = 15; // Reduced from 30 for better performance

    // Use document fragment for better performance
    const fragment = document.createDocumentFragment();

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';

        // Random properties - simplified for performance
        const size = 2 + Math.random() * 3;
        const hue = Math.random() * 360;
        const animationDuration = 15 + Math.random() * 10; // Longer duration

        particle.style.cssText = `
            left: ${Math.random() * 100}%;
            width: ${size}px;
            height: ${size}px;
            background: hsl(${hue}, 70%, 60%);
            animation-duration: ${animationDuration}s;
            animation-delay: ${Math.random() * 20}s;
        `;

        fragment.appendChild(particle);
    }

    particlesContainer.appendChild(fragment);
}

/**
 * Creates energy particles that flow horizontally
 */
function createEnergyParticles() {
    // Disabled for performance optimization
    // This complex animation was causing performance issues
    return;
}

/**
 * Creates spark particles that fall from top
 */
function createSparkParticles() {
    // Disabled for performance optimization
    // This animation was causing performance issues
    return;
}

/**
 * Creates circuit nodes at grid intersections
 */
function createCircuitNodes() {
    // Disabled for performance optimization
    // This complex animation system was causing performance issues
    return;
}

/**
 * Creates data streams flowing through the circuit
 */
function createDataStreams() {
    // Disabled for performance optimization
    return;
}

/**
 * Creates interactive light rays
 */
function createLightRays() {
    // Disabled for performance optimization
    return;
}

/**
 * Sets up interactive background effects
 */
function setupInteractiveBackground() {
    let mouseX = 0;
    let mouseY = 0;

    // Throttled mouse move for better performance
    let mouseThrottle = false;
    document.addEventListener('mousemove', (e) => {
        if (mouseThrottle) return;
        mouseThrottle = true;

        requestAnimationFrame(() => {
            mouseX = e.clientX / window.innerWidth;
            mouseY = e.clientY / window.innerHeight;

            // Simplified interactive orb movement
            const orbs = document.querySelectorAll('.glowing-orb');
            orbs.forEach((orb, index) => {
                const speed = (index + 1) * 0.2; // Reduced speed
                const x = (mouseX - 0.5) * speed * 15; // Reduced movement
                const y = (mouseY - 0.5) * speed * 15;

                orb.style.transform = `translate(${x}px, ${y}px)`;
            });

            mouseThrottle = false;
        });
    });

    // Add click effects
    document.addEventListener('click', (e) => {
        if (e.target.closest('.gaming-station') || e.target.closest('.modal')) return;

        createClickEffect(e.clientX, e.clientY);
    });
}

/**
 * Creates a click effect at the specified coordinates
 */
function createClickEffect(x, y) {
    const effect = document.createElement('div');
    effect.className = 'click-effect';
    effect.style.cssText = `
        position: fixed;
        left: ${x}px;
        top: ${y}px;
        width: 20px;
        height: 20px;
        border: 2px solid var(--primary-color);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
        z-index: 1000;
        animation: clickExpand 0.6s ease-out forwards;
    `;

    document.body.appendChild(effect);

    setTimeout(() => {
        if (document.body.contains(effect)) {
            document.body.removeChild(effect);
        }
    }, 600);
}

// Enhanced Navbar System
function initializeEnhancedNavbar() {
    setupNavbarScrollEffects();
    setupMobileNavigation();
    setupNavigationProgress();
    setupActiveNavigation();
    setupThemeToggle();
}

/**
 * Sets up navbar scroll effects and animations
 */
function setupNavbarScrollEffects() {
    const navbar = document.getElementById('navbar');
    let lastScrollTop = 0;
    let scrollTimeout;

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Add scrolled class for styling
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide/show navbar on scroll
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            navbar.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            navbar.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;

        // Clear timeout and set new one
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
            navbar.style.transform = 'translateY(0)';
        }, 150);
    });
}

/**
 * Sets up mobile navigation functionality
 */
function setupMobileNavigation() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');

        // Prevent body scroll when menu is open
        if (navMenu.classList.contains('active')) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }
    });

    // Close mobile menu when clicking on nav links
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.style.overflow = 'auto';
        });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.style.overflow = 'auto';
        }
    });
}

/**
 * Sets up navigation progress indicator
 */
function setupNavigationProgress() {
    const progressBar = document.getElementById('nav-progress');

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollProgress = (scrollTop / scrollHeight) * 100;

        progressBar.style.width = scrollProgress + '%';
    });
}

/**
 * Sets up active navigation highlighting
 */
function setupActiveNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('section[id]');

    // Intersection Observer for active section detection
    const observerOptions = {
        rootMargin: '-50% 0px -50% 0px',
        threshold: 0
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const activeLink = document.querySelector(`[data-section="${entry.target.id}"]`);

                // Remove active class from all links
                navLinks.forEach(link => link.classList.remove('active'));

                // Add active class to current link
                if (activeLink) {
                    activeLink.classList.add('active');
                }
            }
        });
    }, observerOptions);

    sections.forEach(section => {
        observer.observe(section);
    });

    // Smooth scroll for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('data-section');
            const targetSection = document.getElementById(targetId);

            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for navbar height

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Sets up theme toggle functionality
 */
function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle.querySelector('i');

    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', savedTheme);

    updateThemeIcon(savedTheme);

    themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        updateThemeIcon(newTheme);

        // Add transition effect
        themeToggle.style.transform = 'rotate(360deg)';
        setTimeout(() => {
            themeToggle.style.transform = '';
        }, 300);
    });

    function updateThemeIcon(theme) {
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// Initialize gaming stations
function initializeStations() {
    // Create PC stations
    const pcContainer = document.getElementById('pc-stations');
    for (let i = 1; i <= stationConfigs.pc.count; i++) {
        const station = createStation('pc', i);
        stations.push(station);
        pcContainer.appendChild(station.element);
    }

    // Create PS5 stations
    const ps5Container = document.getElementById('ps5-stations');
    for (let i = 1; i <= stationConfigs.ps5.count; i++) {
        const station = createStation('ps5', i);
        stations.push(station);
        ps5Container.appendChild(station.element);
    }

    // Apply existing reservations
    applyReservations();
}

// Create a gaming station element
function createStation(type, number) {
    const config = stationConfigs[type];
    const stationId = `${type}-${number}`;

    const element = document.createElement('div');
    element.className = `gaming-station ${type}-station available`;
    element.id = stationId;
    element.innerHTML = `
        <i class="${config.icon}"></i>
        <span class="station-number">${number}</span>
    `;

    const station = {
        id: stationId,
        type: type,
        number: number,
        status: 'available',
        element: element,
        config: config
    };

    // Add enhanced click event listener with multi-selection support
    element.addEventListener('click', (e) => handleStationClick(station, e));

    return station;
}

/**
 * Handles station click with multi-selection support
 */
function handleStationClick(station, event) {
    // Check if Ctrl/Cmd key is pressed for multi-selection
    if (event.ctrlKey || event.metaKey) {
        toggleStationSelection(station);
    } else {
        // Single selection mode
        selectSingleStation(station);
    }
}

/**
 * Toggles station selection for multi-selection mode
 */
function toggleStationSelection(station) {
    if (station.status !== 'available') {
        showNotification('This station is not available for selection.', 'error');
        return;
    }

    if (selectedStations.has(station.id)) {
        // Remove from selection
        selectedStations.delete(station.id);
        station.element.classList.remove('multi-selected');
    } else {
        // Add to selection
        selectedStations.add(station.id);
        station.element.classList.add('multi-selected');

        // Create selection effect
        createSelectionParticleBurst(station.element);
    }

    updateMultiSelectionPanel();
    updateAvailabilityCounts();
}

/**
 * Selects a single station (traditional mode)
 */
function selectSingleStation(station) {
    if (station.status !== 'available') {
        // Enhanced feedback for unavailable stations
        station.element.style.animation = 'stationShake 0.5s ease-in-out';
        setTimeout(() => {
            station.element.style.animation = '';
        }, 500);

        const statusMessages = {
            'reserved': 'This station is currently reserved. Please select another station.',
            'in-use-soon': 'This station will be in use soon. Please select another station.'
        };

        showNotification(statusMessages[station.status] || 'This station is not available for reservation.', 'error');
        return;
    }

    // Clear any multi-selections
    clearAllSelections();

    // Remove previous single selection
    if (selectedStation) {
        selectedStation.element.classList.remove('selected');
    }

    // Select new station
    selectedStation = station;
    station.element.classList.add('selected');

    // Create enhanced selection effects
    createSelectionParticleBurst(station.element);
    createSelectionRipple(station.element);

    // Update modal content and show
    updateModalContent(station);
    showModalWithAnimation();
}

/**
 * Sets up multi-selection functionality
 */
function setupMultiSelection() {
    const clearSelectionBtn = document.getElementById('clear-selection');
    const reserveSelectedBtn = document.getElementById('reserve-selected');

    clearSelectionBtn.addEventListener('click', clearAllSelections);
    reserveSelectedBtn.addEventListener('click', reserveMultipleStations);

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            clearAllSelections();
        }
        if (e.key === 'Enter' && selectedStations.size > 0) {
            reserveMultipleStations();
        }
    });
}

/**
 * Updates the multi-selection panel
 */
function updateMultiSelectionPanel() {
    const panel = document.getElementById('multi-selection-panel');
    const selectedStationsContainer = document.getElementById('selected-stations');
    const selectionCount = document.getElementById('selection-count');
    const reserveBtn = document.getElementById('reserve-selected');

    // Show/hide panel based on selection
    if (selectedStations.size > 0) {
        panel.classList.add('active');
    } else {
        panel.classList.remove('active');
    }

    // Update selection count
    selectionCount.textContent = selectedStations.size;

    // Enable/disable reserve button
    reserveBtn.disabled = selectedStations.size === 0;

    // Clear and rebuild selected stations list
    selectedStationsContainer.innerHTML = '';

    selectedStations.forEach(stationId => {
        const station = stations.find(s => s.id === stationId);
        if (station) {
            const stationItem = createSelectedStationItem(station);
            selectedStationsContainer.appendChild(stationItem);
        }
    });
}

/**
 * Creates a selected station item for the panel
 */
function createSelectedStationItem(station) {
    const item = document.createElement('div');
    item.className = 'selected-station-item';
    item.innerHTML = `
        <div class="station-info">
            <i class="${station.config.icon}"></i>
            <span>${station.config.name} ${station.number}</span>
        </div>
        <button class="remove-station" onclick="removeStationFromSelection('${station.id}')">
            <i class="fas fa-times"></i>
        </button>
    `;
    return item;
}

/**
 * Removes a station from multi-selection
 */
function removeStationFromSelection(stationId) {
    const station = stations.find(s => s.id === stationId);
    if (station) {
        selectedStations.delete(stationId);
        station.element.classList.remove('multi-selected');
        updateMultiSelectionPanel();
        updateAvailabilityCounts();
    }
}

/**
 * Clears all selections
 */
function clearAllSelections() {
    // Clear multi-selections
    selectedStations.forEach(stationId => {
        const station = stations.find(s => s.id === stationId);
        if (station) {
            station.element.classList.remove('multi-selected');
        }
    });
    selectedStations.clear();

    // Clear single selection
    if (selectedStation) {
        selectedStation.element.classList.remove('selected');
        selectedStation = null;
    }

    updateMultiSelectionPanel();
    updateAvailabilityCounts();
}

/**
 * Reserves multiple selected stations
 */
function reserveMultipleStations() {
    if (selectedStations.size === 0) {
        showNotification('No stations selected for reservation.', 'error');
        return;
    }

    // Create enhanced modal for multi-reservation
    showMultiReservationModal();
}

/**
 * Updates availability counts in zone headers
 */
function updateAvailabilityCounts() {
    const pcAvailable = stations.filter(s => s.type === 'pc' && s.status === 'available').length;
    const ps5Available = stations.filter(s => s.type === 'ps5' && s.status === 'available').length;

    const pcCounter = document.getElementById('pc-available');
    const ps5Counter = document.getElementById('ps5-available');

    if (pcCounter) pcCounter.textContent = pcAvailable;
    if (ps5Counter) ps5Counter.textContent = ps5Available;
}

/**
 * Shows the multi-reservation modal
 */
function showMultiReservationModal() {
    const modal = document.getElementById('multi-reservation-modal');
    const stationsList = document.getElementById('multi-stations-list');
    const selectionCount = document.getElementById('multi-selection-count');

    // Update selection count
    selectionCount.textContent = selectedStations.size;

    // Clear and populate stations list
    stationsList.innerHTML = '';

    selectedStations.forEach(stationId => {
        const station = stations.find(s => s.id === stationId);
        if (station) {
            const stationItem = document.createElement('div');
            stationItem.className = `station-summary-item ${station.type}-item`;
            stationItem.innerHTML = `
                <i class="${station.config.icon}"></i>
                <span>${station.config.name} ${station.number}</span>
            `;
            stationsList.appendChild(stationItem);
        }
    });

    // Update pricing
    updatePricingSummary();

    // Set minimum date
    const dateField = document.getElementById('multi-reservation-date');
    const today = new Date().toISOString().split('T')[0];
    dateField.min = today;

    // Set default guest count
    document.getElementById('total-guests').value = selectedStations.size;

    // Show modal
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Setup form submission
    const form = document.getElementById('multi-reservation-form');
    form.onsubmit = handleMultiReservationSubmit;

    // Setup pricing updates
    const timeFields = ['multi-start-time', 'multi-end-time'];
    timeFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        field.addEventListener('change', updatePricingSummary);
    });
}

/**
 * Updates the pricing summary
 */
function updatePricingSummary() {
    const pcStations = Array.from(selectedStations).filter(id => id.startsWith('pc-'));
    const ps5Stations = Array.from(selectedStations).filter(id => id.startsWith('ps5-'));

    const pcRate = 5.00; // $5 per hour for PC
    const ps5Rate = 8.00; // $8 per hour for PS5

    const startTime = document.getElementById('multi-start-time').value;
    const endTime = document.getElementById('multi-end-time').value;

    let duration = 1; // Default 1 hour
    if (startTime && endTime) {
        const start = new Date(`2000-01-01T${startTime}`);
        const end = new Date(`2000-01-01T${endTime}`);
        duration = (end - start) / (1000 * 60 * 60); // Convert to hours
    }

    const pcTotal = pcStations.length * pcRate * duration;
    const ps5Total = ps5Stations.length * ps5Rate * duration;
    const grandTotal = pcTotal + ps5Total;

    document.getElementById('pc-count').textContent = pcStations.length;
    document.getElementById('ps5-count').textContent = ps5Stations.length;
    document.getElementById('pc-total').textContent = `$${pcTotal.toFixed(2)}`;
    document.getElementById('ps5-total').textContent = `$${ps5Total.toFixed(2)}`;
    document.getElementById('grand-total').textContent = `$${grandTotal.toFixed(2)}`;
}

/**
 * Handles multi-reservation form submission
 */
function handleMultiReservationSubmit(e) {
    e.preventDefault();

    if (!validateMultiReservationForm()) {
        return;
    }

    const formData = new FormData(e.target);
    const groupReservation = {
        id: generateId(),
        type: 'group',
        stationIds: Array.from(selectedStations),
        customerName: formData.get('name'),
        customerEmail: formData.get('email'),
        customerPhone: formData.get('phone'),
        totalGuests: parseInt(formData.get('guests')),
        date: formData.get('date'),
        startTime: formData.get('startTime'),
        endTime: formData.get('endTime'),
        timestamp: new Date().toISOString()
    };

    // Create individual reservations for each station
    selectedStations.forEach(stationId => {
        const station = stations.find(s => s.id === stationId);
        if (station) {
            const reservation = {
                id: generateId(),
                stationId: stationId,
                stationType: station.type,
                stationNumber: station.number,
                customerName: groupReservation.customerName,
                customerEmail: groupReservation.customerEmail,
                customerPhone: groupReservation.customerPhone,
                date: groupReservation.date,
                startTime: groupReservation.startTime,
                endTime: groupReservation.endTime,
                groupId: groupReservation.id,
                timestamp: groupReservation.timestamp
            };

            reservations.push(reservation);
            updateStationStatus(stationId, 'reserved');
        }
    });

    // Save reservations
    localStorage.setItem('reservations', JSON.stringify(reservations));

    // Show confirmation
    showGroupConfirmation(groupReservation);

    // Clear selections and close modal
    clearAllSelections();
    closeMultiReservationModal();

    // Update status bar
    updateStatusBar();
    updateAvailabilityCounts();
}

/**
 * Validates the multi-reservation form
 */
function validateMultiReservationForm() {
    // Basic validation - can be enhanced
    const requiredFields = ['name', 'email', 'phone', 'guests', 'date', 'startTime', 'endTime'];
    let isValid = true;

    requiredFields.forEach(fieldName => {
        const field = document.querySelector(`#multi-reservation-form [name="${fieldName}"]`);
        if (!field.value.trim()) {
            showFieldError(fieldName, 'This field is required');
            isValid = false;
        }
    });

    // Validate guest count
    const guestCount = parseInt(document.getElementById('total-guests').value);
    if (guestCount > selectedStations.size) {
        showFieldError('guests', 'Number of guests cannot exceed selected stations');
        isValid = false;
    }

    return isValid;
}

/**
 * Closes the multi-reservation modal
 */
function closeMultiReservationModal() {
    const modal = document.getElementById('multi-reservation-modal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

/**
 * Shows group reservation confirmation
 */
function showGroupConfirmation(groupReservation) {
    const modal = document.getElementById('confirmation-modal');
    const detailsElement = document.getElementById('confirmation-details');

    detailsElement.innerHTML = `
        <div style="margin-bottom: 15px;">
            <strong>Group Reservation:</strong> ${groupReservation.stationIds.length} Stations
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Group Leader:</strong> ${groupReservation.customerName}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Email:</strong> ${groupReservation.customerEmail}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Phone:</strong> ${groupReservation.customerPhone}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Total Guests:</strong> ${groupReservation.totalGuests}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Date:</strong> ${formatDate(groupReservation.date)}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Time:</strong> ${groupReservation.startTime} - ${groupReservation.endTime}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Group ID:</strong> ${groupReservation.id}
        </div>
    `;

    modal.style.display = 'block';

    // Trigger confetti effect
    createConfettiEffect();
}

// Setup event listeners
function setupEventListeners() {
    // Modal close events
    document.querySelector('.close-modal').addEventListener('click', closeModal);
    document.getElementById('reservation-modal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });

    // Form submission
    document.getElementById('reservation-form').addEventListener('submit', handleReservation);

    // Navigation smooth scrolling
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // Form validation on input
    const inputs = document.querySelectorAll('#reservation-form input');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearError);
    });
}

// Select a gaming station
function selectStation(station) {
    if (station.status !== 'available') {
        showNotification('This station is not available for reservation.', 'error');
        return;
    }

    // Remove previous selection
    if (selectedStation) {
        selectedStation.element.classList.remove('selected');
    }

    // Select new station
    selectedStation = station;
    station.element.classList.add('selected');

    // Update modal content
    updateModalContent(station);

    // Show modal
    document.getElementById('reservation-modal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Update modal content with selected station info
function updateModalContent(station) {
    const config = station.config;
    document.getElementById('modal-title').textContent = `Reserve ${config.name} ${station.number}`;
    document.getElementById('selected-station-name').textContent = `${config.name} ${station.number}`;
    document.getElementById('selected-station-specs').textContent = config.specs;
    
    const iconElement = document.getElementById('selected-station-icon');
    iconElement.innerHTML = `<i class="${config.icon}"></i>`;
}

// Close reservation modal
function closeModal() {
    document.getElementById('reservation-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
    
    if (selectedStation) {
        selectedStation.element.classList.remove('selected');
        selectedStation = null;
    }
    
    clearForm();
}

// Handle reservation form submission
function handleReservation(e) {
    e.preventDefault();
    
    if (!validateForm()) {
        return;
    }

    const formData = new FormData(e.target);
    const reservation = {
        id: generateId(),
        stationId: selectedStation.id,
        stationType: selectedStation.type,
        stationNumber: selectedStation.number,
        customerName: formData.get('name'),
        customerEmail: formData.get('email'),
        customerPhone: formData.get('phone'),
        date: formData.get('date'),
        startTime: formData.get('startTime'),
        endTime: formData.get('endTime'),
        timestamp: new Date().toISOString()
    };

    // Save reservation
    reservations.push(reservation);
    localStorage.setItem('reservations', JSON.stringify(reservations));

    // Update station status
    updateStationStatus(selectedStation.id, 'reserved');

    // Show confirmation
    showConfirmation(reservation);

    // Close modal
    closeModal();

    // Update status bar
    updateStatusBar();
}

// Validate form
function validateForm() {
    let isValid = true;
    const requiredFields = ['name', 'email', 'phone', 'date', 'startTime', 'endTime'];
    
    requiredFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });

    // Validate time range
    const startTime = document.getElementById('start-time').value;
    const endTime = document.getElementById('end-time').value;
    
    if (startTime && endTime && startTime >= endTime) {
        showFieldError('end-time', 'End time must be after start time');
        isValid = false;
    }

    return isValid;
}

// Validate individual field
function validateField(e) {
    const field = e.target;
    const value = field.value.trim();
    const fieldName = field.name;
    
    clearFieldError(fieldName);
    
    if (!value) {
        showFieldError(fieldName, 'This field is required');
        return false;
    }
    
    switch (fieldName) {
        case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                showFieldError(fieldName, 'Please enter a valid email address');
                return false;
            }
            break;
            
        case 'phone':
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
                showFieldError(fieldName, 'Please enter a valid phone number');
                return false;
            }
            break;
            
        case 'date':
            const selectedDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                showFieldError(fieldName, 'Please select a future date');
                return false;
            }
            break;
    }
    
    return true;
}

// Show field error
function showFieldError(fieldName, message) {
    const field = document.querySelector(`[name="${fieldName}"]`);
    const errorElement = field.parentNode.querySelector('.error-message');
    
    field.style.borderColor = 'var(--danger-color)';
    errorElement.textContent = message;
    errorElement.style.display = 'block';
}

// Clear field error
function clearFieldError(fieldName) {
    const field = document.querySelector(`[name="${fieldName}"]`);
    const errorElement = field.parentNode.querySelector('.error-message');
    
    field.style.borderColor = 'rgba(0, 255, 255, 0.3)';
    errorElement.style.display = 'none';
}

// Clear error on input
function clearError(e) {
    clearFieldError(e.target.name);
}

// Show confirmation modal
function showConfirmation(reservation) {
    const modal = document.getElementById('confirmation-modal');
    const detailsElement = document.getElementById('confirmation-details');
    
    const config = stationConfigs[reservation.stationType];
    
    detailsElement.innerHTML = `
        <div style="margin-bottom: 15px;">
            <strong>Station:</strong> ${config.name} ${reservation.stationNumber}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Customer:</strong> ${reservation.customerName}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Email:</strong> ${reservation.customerEmail}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Phone:</strong> ${reservation.customerPhone}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Date:</strong> ${formatDate(reservation.date)}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Time:</strong> ${reservation.startTime} - ${reservation.endTime}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>Reservation ID:</strong> ${reservation.id}
        </div>
    `;
    
    modal.style.display = 'block';
}

// Close confirmation modal
function closeConfirmationModal() {
    document.getElementById('confirmation-modal').style.display = 'none';
}

// Update station status
function updateStationStatus(stationId, status) {
    const station = stations.find(s => s.id === stationId);
    if (station) {
        station.status = status;
        station.element.className = `gaming-station ${status}`;
    }
}

// Apply existing reservations
function applyReservations() {
    const now = new Date();
    
    reservations.forEach(reservation => {
        const reservationDate = new Date(reservation.date + 'T' + reservation.startTime);
        const endDate = new Date(reservation.date + 'T' + reservation.endTime);
        
        if (endDate < now) {
            // Reservation has ended, remove it
            return;
        }
        
        let status = 'reserved';
        if (reservationDate <= now && now <= endDate) {
            status = 'in-use-soon';
        }
        
        updateStationStatus(reservation.stationId, status);
    });
    
    // Clean up expired reservations
    reservations = reservations.filter(reservation => {
        const endDate = new Date(reservation.date + 'T' + reservation.endTime);
        return endDate >= now;
    });
    
    localStorage.setItem('reservations', JSON.stringify(reservations));
}

// Update status bar
function updateStatusBar() {
    const available = stations.filter(s => s.status === 'available').length;
    const reserved = stations.filter(s => s.status === 'reserved').length;
    const inUseSoon = stations.filter(s => s.status === 'in-use-soon').length;
    
    document.getElementById('available-count').textContent = available;
    document.getElementById('reserved-count').textContent = reserved;
    document.getElementById('in-use-count').textContent = inUseSoon;
    document.getElementById('last-updated-time').textContent = new Date().toLocaleTimeString();
}

// Start status updater
function startStatusUpdater() {
    setInterval(() => {
        applyReservations();
        updateStatusBar();
    }, 120000); // Update every 2 minutes for better performance
}

// Set minimum date for reservation
function setMinDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('reservation-date').min = today;
}

// Clear form
function clearForm() {
    document.getElementById('reservation-form').reset();
    document.querySelectorAll('.error-message').forEach(error => {
        error.style.display = 'none';
    });
    document.querySelectorAll('#reservation-form input').forEach(input => {
        input.style.borderColor = 'rgba(0, 255, 255, 0.3)';
    });
}

// Utility functions
function generateId() {
    return 'res_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'error' ? 'var(--danger-color)' : 'var(--primary-color)'};
        color: var(--dark-bg);
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 3000;
        font-weight: 600;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Smooth scroll to hall section
function scrollToHall() {
    document.getElementById('hall').scrollIntoView({ behavior: 'smooth' });
}

// Advanced Features and Animations

// Countdown timers for reserved stations
let countdownTimers = {};

// Add countdown timer to station
function addCountdownTimer(stationId, endTime) {
    const station = stations.find(s => s.id === stationId);
    if (!station) return;

    // Create timer element
    const timerElement = document.createElement('div');
    timerElement.className = 'countdown-timer';
    station.element.appendChild(timerElement);

    // Update timer every second
    const timerId = setInterval(() => {
        const now = new Date();
        const timeLeft = endTime - now;

        if (timeLeft <= 0) {
            clearInterval(timerId);
            station.element.removeChild(timerElement);
            delete countdownTimers[stationId];
            updateStationStatus(stationId, 'available');
            updateStatusBar();
            return;
        }

        const minutes = Math.floor(timeLeft / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);

    countdownTimers[stationId] = timerId;
}

// Enhanced station selection with particle effects
function selectStationWithEffects(station) {
    // Create particle effect
    createParticleEffect(station.element);

    // Add selection sound effect (visual feedback)
    station.element.style.transform = 'scale(1.2)';
    setTimeout(() => {
        station.element.style.transform = '';
    }, 200);

    selectStation(station);
}

// Create particle effect
function createParticleEffect(element) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    for (let i = 0; i < 8; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = centerX + 'px';
        particle.style.top = centerY + 'px';
        particle.style.animationDelay = (i * 0.1) + 's';

        // Random direction
        const angle = (i * 45) * Math.PI / 180;
        const distance = 50;
        const endX = centerX + Math.cos(angle) * distance;
        const endY = centerY + Math.sin(angle) * distance;

        particle.style.setProperty('--end-x', endX + 'px');
        particle.style.setProperty('--end-y', endY + 'px');

        document.body.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (document.body.contains(particle)) {
                document.body.removeChild(particle);
            }
        }, 3000);
    }
}

// Enhanced reservation handling with animations
function handleReservationWithAnimation(e) {
    e.preventDefault();

    if (!validateForm()) {
        // Shake form on validation error
        const form = document.getElementById('reservation-form');
        form.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            form.style.animation = '';
        }, 500);
        return;
    }

    // Show loading state
    const submitButton = document.querySelector('.btn-reserve');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<div class="loading-spinner"></div> Processing...';
    submitButton.disabled = true;

    // Simulate processing delay for better UX
    setTimeout(() => {
        const formData = new FormData(e.target);
        const reservation = {
            id: generateId(),
            stationId: selectedStation.id,
            stationType: selectedStation.type,
            stationNumber: selectedStation.number,
            customerName: formData.get('name'),
            customerEmail: formData.get('email'),
            customerPhone: formData.get('phone'),
            date: formData.get('date'),
            startTime: formData.get('startTime'),
            endTime: formData.get('endTime'),
            timestamp: new Date().toISOString()
        };

        // Save reservation
        reservations.push(reservation);
        localStorage.setItem('reservations', JSON.stringify(reservations));

        // Update station status with animation
        updateStationStatusWithAnimation(selectedStation.id, 'reserved');

        // Add countdown timer if reservation is for today
        const reservationDate = new Date(reservation.date);
        const today = new Date();
        if (reservationDate.toDateString() === today.toDateString()) {
            const endTime = new Date(reservation.date + 'T' + reservation.endTime);
            addCountdownTimer(selectedStation.id, endTime);
        }

        // Show confirmation with animation
        showConfirmationWithAnimation(reservation);

        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;

        // Close modal
        closeModal();

        // Update status bar
        updateStatusBar();
    }, 1500);
}

// Update station status with animation
function updateStationStatusWithAnimation(stationId, status) {
    const station = stations.find(s => s.id === stationId);
    if (station) {
        station.status = status;

        // Animate status change
        station.element.style.transform = 'scale(0.8)';
        station.element.style.opacity = '0.5';

        setTimeout(() => {
            station.element.className = `gaming-station ${status}`;
            station.element.style.transform = 'scale(1.1)';
            station.element.style.opacity = '1';

            setTimeout(() => {
                station.element.style.transform = '';
            }, 300);
        }, 200);
    }
}

// Enhanced confirmation modal with animation
function showConfirmationWithAnimation(reservation) {
    const modal = document.getElementById('confirmation-modal');
    const detailsElement = document.getElementById('confirmation-details');

    const config = stationConfigs[reservation.stationType];

    detailsElement.innerHTML = `
        <div style="margin-bottom: 15px; animation: slideInLeft 0.5s ease;">
            <strong>Station:</strong> ${config.name} ${reservation.stationNumber}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.6s ease;">
            <strong>Customer:</strong> ${reservation.customerName}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.7s ease;">
            <strong>Email:</strong> ${reservation.customerEmail}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.8s ease;">
            <strong>Phone:</strong> ${reservation.customerPhone}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 0.9s ease;">
            <strong>Date:</strong> ${formatDate(reservation.date)}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 1s ease;">
            <strong>Time:</strong> ${reservation.startTime} - ${reservation.endTime}
        </div>
        <div style="margin-bottom: 15px; animation: slideInLeft 1.1s ease;">
            <strong>Reservation ID:</strong> ${reservation.id}
        </div>
    `;

    modal.style.display = 'block';

    // Trigger confetti effect
    createConfettiEffect();
}

// Create confetti effect
function createConfettiEffect() {
    const colors = ['#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff0040'];

    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: ${colors[Math.floor(Math.random() * colors.length)]};
            top: -10px;
            left: ${Math.random() * 100}vw;
            z-index: 3000;
            animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
            transform: rotate(${Math.random() * 360}deg);
        `;

        document.body.appendChild(confetti);

        setTimeout(() => {
            if (document.body.contains(confetti)) {
                document.body.removeChild(confetti);
            }
        }, 5000);
    }
}

// Update event listeners to use enhanced functions
function setupEnhancedEventListeners() {
    // Replace form submission handler
    document.getElementById('reservation-form').removeEventListener('submit', handleReservation);
    document.getElementById('reservation-form').addEventListener('submit', handleReservationWithAnimation);

    // Update station click handlers
    stations.forEach(station => {
        station.element.removeEventListener('click', () => selectStation(station));
        station.element.addEventListener('click', () => selectStationWithEffects(station));
    });
}

// Add CSS animations for notifications and new effects
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    @keyframes slideInLeft {
        from { transform: translateX(-30px); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    @keyframes confettiFall {
        0% { transform: translateY(-10px) rotate(0deg); opacity: 1; }
        100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .particle {
        animation: particleExplode 1s ease-out forwards;
    }

    @keyframes particleExplode {
        0% {
            transform: translate(0, 0) scale(1);
            opacity: 1;
        }
        100% {
            transform: translate(var(--end-x, 0), var(--end-y, 0)) scale(0);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', function() {
    // Wait for initial setup to complete
    setTimeout(() => {
        setupEnhancedEventListeners();
    }, 100);
});
